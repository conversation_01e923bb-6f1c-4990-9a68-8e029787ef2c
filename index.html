<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Stock Tracker</title>
  <!-- Tailwind CSS CDN -->
  <script src="https://cdn.tailwindcss.com"></script>
  <link rel="stylesheet" href="style.css" />
</head>
<body class="bg-slate-50 text-slate-800">
  <!-- Top Nav -->
  <header class="bg-white/80 backdrop-blur supports-[backdrop-filter]:bg-white/60 border-b border-slate-200 sticky top-0 z-10">
    <div class="max-w-6xl mx-auto px-4">
      <div class="h-14 flex items-center justify-between">
        <h1 class="text-xl sm:text-2xl font-bold tracking-tight">📈 Stock Tracker</h1>
        <a href="https://tailwindcss.com" target="_blank" class="hidden sm:inline-flex text-sm text-slate-500 hover:text-slate-700">Built with Tailwind</a>
      </div>
    </div>
  </header>

  <main class="max-w-6xl mx-auto px-4 py-6 sm:py-10">
    <!-- Form + Search Row -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 lg:gap-8 items-start">
      <!-- Form Card -->
      <section class="lg:col-span-1">
        <div class="bg-white rounded-xl shadow-sm ring-1 ring-slate-200 p-5 sm:p-6">
          <div class="flex items-center justify-between mb-4">
            <h2 id="formTitle" class="text-lg font-semibold">Add Stock</h2>
            <span id="editBadge" class="hidden text-xs px-2 py-1 rounded-full bg-amber-100 text-amber-700">Editing</span>
          </div>
          <form id="stockForm" class="space-y-4">
            <input type="hidden" id="stockId" />

            <div class="grid grid-cols-1 sm:grid-cols-3 gap-3">
              <div class="sm:col-span-2">
                <label class="block text-sm font-medium mb-1" for="name">Stock Name</label>
                <input id="name" name="name" type="text" required placeholder="e.g., AAPL, INFY"
                  class="w-full rounded-lg border-slate-300 focus:border-sky-400 focus:ring-sky-400" />
              </div>
              <div>
                <label class="block text-sm font-medium mb-1" for="currency">Currency</label>
                <select id="currency" name="currency" class="w-full rounded-lg border-slate-300 focus:border-sky-400 focus:ring-sky-400">
                  <option value="₹">₹ INR</option>
                  <option value="$">$ USD</option>
                </select>
              </div>
            </div>

            <div class="grid grid-cols-1 sm:grid-cols-2 gap-3">
              <div>
                <label class="block text-sm font-medium mb-1" for="buyPrice">Buy Price</label>
                <input id="buyPrice" name="buyPrice" type="number" min="0" step="0.01" required placeholder="0.00"
                  class="w-full rounded-lg border-slate-300 focus:border-sky-400 focus:ring-sky-400" />
              </div>
              <div>
                <label class="block text-sm font-medium mb-1" for="targetPrice">Target Price</label>
                <input id="targetPrice" name="targetPrice" type="number" min="0" step="0.01" required placeholder="0.00"
                  class="w-full rounded-lg border-slate-300 focus:border-sky-400 focus:ring-sky-400" />
              </div>
            </div>

            <div class="grid grid-cols-1 sm:grid-cols-2 gap-3">
              <div>
                <label class="block text-sm font-medium mb-1" for="timePeriod">Time Period</label>
                <input id="timePeriod" name="timePeriod" type="text" placeholder="YYYY-MM-DD or 'Long-term'"
                  class="w-full rounded-lg border-slate-300 focus:border-sky-400 focus:ring-sky-400" />
              </div>
              <div>
                <label class="block text-sm font-medium mb-1" for="currentPrice">Current Price (optional)</label>
                <input id="currentPrice" name="currentPrice" type="number" min="0" step="0.01" placeholder="0.00"
                  class="w-full rounded-lg border-slate-300 focus:border-sky-400 focus:ring-sky-400" />
              </div>
            </div>

            <div>
              <label class="block text-sm font-medium mb-1" for="remarks">Remarks / Notes</label>
              <textarea id="remarks" name="remarks" rows="3" placeholder="Why this stock? Any notes..."
                class="w-full rounded-lg border-slate-300 focus:border-sky-400 focus:ring-sky-400"></textarea>
            </div>

            <div class="flex items-center gap-3 pt-2">
              <button type="submit" class="inline-flex items-center justify-center rounded-lg bg-sky-600 hover:bg-sky-700 text-white px-4 py-2 text-sm font-medium shadow-sm">
                Save Stock
              </button>
              <button type="reset" id="resetBtn" class="inline-flex items-center justify-center rounded-lg bg-slate-100 hover:bg-slate-200 text-slate-700 px-4 py-2 text-sm font-medium">
                Reset
              </button>
              <button type="button" id="cancelEditBtn" class="hidden ml-auto text-sm text-slate-500 hover:text-slate-700">Cancel editing</button>
            </div>
          </form>
        </div>
      </section>

      <!-- Search + List -->
      <section class="lg:col-span-2">
        <div class="flex flex-col gap-4">
          <!-- Search -->
          <div class="bg-white rounded-xl shadow-sm ring-1 ring-slate-200 p-4">
            <div class="flex items-center gap-3">
              <div class="relative flex-1">
                <span class="pointer-events-none absolute inset-y-0 left-0 pl-3 flex items-center text-slate-400">🔎</span>
                <input id="searchInput" type="text" placeholder="Search by stock name..."
                  class="w-full pl-10 rounded-lg border-slate-300 focus:border-sky-400 focus:ring-sky-400" />
              </div>
              <button id="clearSearchBtn" class="hidden text-sm text-slate-500 hover:text-slate-700">Clear</button>
            </div>
          </div>

          <!-- Cards Grid -->
          <div id="emptyState" class="hidden text-center py-16 bg-white rounded-xl ring-1 ring-slate-200">
            <p class="text-slate-600">No stocks yet. Add your first stock using the form.</p>
          </div>
          <div id="stocksGrid" class="grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-3 gap-6"></div>
        </div>
      </section>
    </div>
  </main>

  <footer class="text-center text-xs text-slate-500 py-8">
    <p>Data is stored locally in your browser (localStorage).</p>
  </footer>

  <script src="script.js"></script>
</body>
</html>

