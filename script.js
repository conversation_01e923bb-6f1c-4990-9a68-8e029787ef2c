// Stock Tracker - <PERSON>illa JS with localStorage persistence
// Data shape: { id, name, currency, buyPrice, targetPrice, currentPrice?, timePeriod, remarks, createdAt }

(function () {
  'use strict';

  // DOM refs
  const form = document.getElementById('stockForm');
  const formTitle = document.getElementById('formTitle');
  const editBadge = document.getElementById('editBadge');
  const cancelEditBtn = document.getElementById('cancelEditBtn');
  const resetBtn = document.getElementById('resetBtn');

  const idField = document.getElementById('stockId');
  const nameField = document.getElementById('name');
  const currencyField = document.getElementById('currency');
  const buyField = document.getElementById('buyPrice');
  const targetField = document.getElementById('targetPrice');
  const currentField = document.getElementById('currentPrice');
  const periodField = document.getElementById('timePeriod');
  const remarksField = document.getElementById('remarks');

  const grid = document.getElementById('stocksGrid');
  const emptyState = document.getElementById('emptyState');

  const searchInput = document.getElementById('searchInput');
  const clearSearchBtn = document.getElementById('clearSearchBtn');

  // Storage helpers
  const STORAGE_KEY = 'stock-tracker:stocks';

  function readStocks() {
    try {
      const raw = localStorage.getItem(STORAGE_KEY);
      if (!raw) return [];
      const parsed = JSON.parse(raw);
      return Array.isArray(parsed) ? parsed : [];
    } catch (e) {
      console.error('Failed to read stocks from localStorage', e);
      return [];
    }
  }

  function writeStocks(stocks) {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(stocks));
  }

  function upsertStock(stock) {
    const stocks = readStocks();
    const idx = stocks.findIndex(s => s.id === stock.id);
    if (idx >= 0) {
      stocks[idx] = stock;
    } else {
      stocks.push(stock);
    }
    writeStocks(stocks);
  }

  function deleteStock(id) {
    const stocks = readStocks().filter(s => s.id !== id);
    writeStocks(stocks);
  }

  // UI rendering
  function formatCurrency(symbol, value) {
    if (value === '' || value === undefined || value === null) return '-';
    const num = Number(value);
    if (Number.isNaN(num)) return '-';
    return `${symbol} ${num.toLocaleString(undefined, { maximumFractionDigits: 2 })}`;
  }

  function calcProfitPct(buy, target) {
    const b = Number(buy);
    const t = Number(target);
    if (!isFinite(b) || !isFinite(t) || b <= 0) return null;
    const pct = ((t - b) / b) * 100;
    return Math.round(pct * 10) / 10; // 1 decimal
  }

  // Date formatting functions
  function formatDateToDDMMYYYY(dateStr) {
    if (!dateStr) return '-';
    try {
      const date = new Date(dateStr);
      if (isNaN(date.getTime())) return dateStr; // fallback to original if invalid
      const day = String(date.getDate()).padStart(2, '0');
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const year = date.getFullYear();
      return `${day}/${month}/${year}`;
    } catch (e) {
      return dateStr; // fallback to original
    }
  }

  function formatTimestamp(timestamp) {
    if (!timestamp) return '-';
    try {
      const date = new Date(timestamp);
      if (isNaN(date.getTime())) return '-';
      const day = String(date.getDate()).padStart(2, '0');
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const year = date.getFullYear();

      // Convert to 12-hour format
      let hours = date.getHours();
      const minutes = String(date.getMinutes()).padStart(2, '0');
      const ampm = hours >= 12 ? 'PM' : 'AM';
      hours = hours % 12;
      hours = hours ? hours : 12; // 0 should be 12
      const formattedHours = String(hours).padStart(2, '0');

      return `${day}/${month}/${year} ${formattedHours}:${minutes} ${ampm}`;
    } catch (e) {
      return '-';
    }
  }

  function convertDateToYYYYMMDD(dateStr) {
    // Convert dd/mm/yyyy to yyyy-mm-dd for date input
    if (!dateStr || dateStr === '-') return '';
    if (dateStr.includes('-')) return dateStr; // already in correct format
    if (dateStr.includes('/')) {
      const parts = dateStr.split('/');
      if (parts.length === 3) {
        const [day, month, year] = parts;
        return `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
      }
    }
    return dateStr;
  }

  function renderStocks(filterText = '') {
    const stocks = readStocks();

    // sort by createdAt desc
    stocks.sort((a, b) => (b.createdAt || 0) - (a.createdAt || 0));

    const q = filterText.trim().toLowerCase();
    const list = q ? stocks.filter(s => (s.name || '').toLowerCase().includes(q)) : stocks;

    grid.innerHTML = '';
    emptyState.classList.toggle('hidden', list.length !== 0);

    list.forEach(stock => {
      const hit = isTargetHit(stock);
      const profit = calcProfitPct(stock.buyPrice, stock.targetPrice);

      const card = document.createElement('div');
      card.className = `stock-card bg-white rounded-xl ring-1 ring-slate-200 shadow-sm p-5 ${hit ? 'stock-card-hit' : ''}`;

      // Header with stock name and actions - better layout
      const header = document.createElement('div');
      header.className = 'mb-4';
      header.innerHTML = `
        <div class="flex items-start justify-between gap-3 mb-2">
          <h3 class="text-lg font-semibold tracking-tight text-slate-900 leading-tight flex-1 min-w-0">
            ${escapeHtml(stock.name || '—')}
          </h3>
          <div class="flex items-center gap-1 flex-shrink-0">
            <button class="icon-btn edit-btn" title="Edit">✏️</button>
            <button class="icon-btn delete-btn" title="Delete">🗑</button>
          </div>
        </div>
        ${profit !== null ? `
          <div class="flex items-center gap-2">
            <span class="badge badge-profit">+${profit}%</span>
            ${hit ? '<span class="badge badge-hit">🎯 Target Hit!</span>' : ''}
          </div>
        ` : hit ? '<div><span class="badge badge-hit">🎯 Target Hit!</span></div>' : ''}
      `;

      // Add event listeners to the buttons
      const editBtn = header.querySelector('.edit-btn');
      const delBtn = header.querySelector('.delete-btn');
      editBtn.addEventListener('click', () => startEditing(stock));
      delBtn.addEventListener('click', () => confirmDelete(stock));

      const body = document.createElement('div');
      body.className = 'space-y-4';
      body.innerHTML = `
        <!-- Price Information -->
        <div class="grid grid-cols-2 gap-4">
          <div class="bg-slate-50 rounded-lg p-3">
            <div class="text-xs font-medium text-slate-500 uppercase tracking-wide mb-1">Buy Price</div>
            <div class="text-lg font-semibold text-slate-900">${formatCurrency(stock.currency || '₹', stock.buyPrice)}</div>
          </div>
          <div class="bg-slate-50 rounded-lg p-3">
            <div class="text-xs font-medium text-slate-500 uppercase tracking-wide mb-1">Target Price</div>
            <div class="text-lg font-semibold text-slate-900">${formatCurrency(stock.currency || '₹', stock.targetPrice)}</div>
          </div>
        </div>

        <!-- Additional Information -->
        <div class="grid grid-cols-2 gap-4 text-sm">
          <div>
            <div class="text-slate-500 font-medium mb-1">Target Date</div>
            <div class="text-slate-900">${formatDateToDDMMYYYY(stock.timePeriod)}</div>
          </div>
          <div>
            <div class="text-slate-500 font-medium mb-1">Current Price</div>
            <div class="text-slate-900 ${hit ? 'text-emerald-600 font-semibold' : ''}">${formatCurrency(stock.currency || '₹', stock.currentPrice)}</div>
          </div>
        </div>

        ${stock.remarks ? `
          <div class="bg-blue-50 rounded-lg p-3">
            <div class="text-xs font-medium text-blue-700 uppercase tracking-wide mb-1">Notes</div>
            <div class="text-sm text-blue-900 whitespace-pre-wrap">${escapeHtml(stock.remarks)}</div>
          </div>
        ` : ''}

        <!-- Timestamp -->
        <div class="pt-3 border-t border-slate-200">
          <div class="text-xs text-slate-400 flex items-center gap-1">
            <span>📅</span>
            <span>Created: ${formatTimestamp(stock.createdAt)}</span>
          </div>
        </div>
      `;

      card.append(actions, header, body);
      grid.appendChild(card);
    });
  }

  function isTargetHit(stock) {
    const c = Number(stock.currentPrice);
    const t = Number(stock.targetPrice);
    if (!isFinite(c) || !isFinite(t)) return false;
    return c >= t;
  }

  function escapeHtml(str) {
    return String(str)
      .replaceAll('&', '&amp;')
      .replaceAll('<', '&lt;')
      .replaceAll('>', '&gt;')
      .replaceAll('"', '&quot;')
      .replaceAll("'", '&#039;');
  }

  // Form handling
  function readForm() {
    return {
      id: idField.value || crypto.randomUUID(),
      name: nameField.value.trim(),
      currency: currencyField.value || '₹',
      buyPrice: parseNumber(buyField.value),
      targetPrice: parseNumber(targetField.value),
      currentPrice: parseNumber(currentField.value),
      timePeriod: periodField.value.trim(),
      remarks: remarksField.value.trim(),
      createdAt: Date.now(),
    };
  }

  function parseNumber(v) {
    if (v === '' || v === null || v === undefined) return '';
    const n = Number(v);
    return Number.isNaN(n) ? '' : n;
  }

  function validate(stock) {
    if (!stock.name) return 'Stock name is required';
    if (stock.buyPrice === '' || stock.targetPrice === '') return 'Buy and Target prices are required';
    if (Number(stock.buyPrice) < 0 || Number(stock.targetPrice) < 0) return 'Prices must be positive';
    return null;
  }

  function startEditing(stock) {
    idField.value = stock.id;
    nameField.value = stock.name || '';
    currencyField.value = stock.currency || '₹';
    buyField.value = stock.buyPrice ?? '';
    targetField.value = stock.targetPrice ?? '';
    currentField.value = stock.currentPrice ?? '';
    periodField.value = convertDateToYYYYMMDD(stock.timePeriod) || '';
    remarksField.value = stock.remarks || '';

    formTitle.textContent = 'Edit Stock';
    editBadge.classList.remove('hidden');
    cancelEditBtn.classList.remove('hidden');
  }

  function stopEditing() {
    idField.value = '';
    form.reset();
    formTitle.textContent = 'Add Stock';
    editBadge.classList.add('hidden');
    cancelEditBtn.classList.add('hidden');
  }

  function confirmDelete(stock) {
    const ok = confirm(`Delete ${stock.name}? This cannot be undone.`);
    if (!ok) return;
    deleteStock(stock.id);
    renderStocks(searchInput.value);
    if (idField.value === stock.id) {
      stopEditing();
    }
  }

  // Listeners
  form.addEventListener('submit', (e) => {
    e.preventDefault();
    const draft = readForm();
    const error = validate(draft);
    if (error) {
      alert(error);
      return;
    }

    // Preserve createdAt when updating
    const existing = readStocks().find(s => s.id === draft.id);
    if (existing) draft.createdAt = existing.createdAt;

    upsertStock(draft);
    renderStocks(searchInput.value);
    stopEditing();
  });

  resetBtn.addEventListener('click', () => {
    stopEditing();
  });

  cancelEditBtn.addEventListener('click', () => {
    stopEditing();
  });

  searchInput.addEventListener('input', () => {
    const has = searchInput.value.trim().length > 0;
    clearSearchBtn.classList.toggle('hidden', !has);
    renderStocks(searchInput.value);
  });

  clearSearchBtn.addEventListener('click', () => {
    searchInput.value = '';
    clearSearchBtn.classList.add('hidden');
    renderStocks('');
  });

  // Init
  document.addEventListener('DOMContentLoaded', () => {
    renderStocks('');
  });
})();

