/* Optional custom styles on top of Tailwind */
:root {
  --card-ok: #ecfeff; /* sky-50 */
  --card-hit: #ecfdf5; /* emerald-50 */
  --ring-hit: #34d399; /* emerald-400 */
}

html, body {
  height: 100%;
}

/******** Cards helpers ********/
.stock-card-hit {
  background: var(--card-hit);
  box-shadow: inset 0 0 0 1px var(--ring-hit);
}

.badge {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  border-radius: 9999px;
  padding: 0.125rem 0.5rem;
  font-size: 0.75rem;
  line-height: 1rem;
}

.badge-profit {
  background: #eff6ff; /* blue-50 */
  color: #1d4ed8;      /* blue-700 */
}

.badge-hit {
  background: #ecfdf5; /* emerald-50 */
  color: #047857;      /* emerald-700 */
  font-weight: 600;
}

.icon-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 32px; height: 32px;
  border-radius: 0.5rem;
  transition: all 150ms ease;
  font-size: 14px;
  border: 1px solid transparent;
}
.icon-btn:hover {
  background: rgba(15,23,42,0.08);
  border-color: rgba(15,23,42,0.1);
  transform: translateY(-1px);
}
.icon-btn:active {
  transform: translateY(0);
}

/* Card animations and improvements */
.stock-card {
  transition: all 200ms ease;
}

.stock-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Price highlight animations */
.price-highlight {
  animation: pulse-green 2s infinite;
}

@keyframes pulse-green {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.8; }
}

/* Hide number input spinners for a cleaner look */
input[type=number]::-webkit-outer-spin-button,
input[type=number]::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
input[type=number] {
  -moz-appearance: textfield;
}

