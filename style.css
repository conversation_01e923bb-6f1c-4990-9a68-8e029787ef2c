/* Optional custom styles on top of Tailwind */
:root {
  --card-ok: #ecfeff; /* sky-50 */
  --card-hit: #ecfdf5; /* emerald-50 */
  --ring-hit: #34d399; /* emerald-400 */
}

html, body {
  height: 100%;
}

/******** Cards helpers ********/
.stock-card-hit {
  background: var(--card-hit);
  box-shadow: inset 0 0 0 1px var(--ring-hit);
}

.badge {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  border-radius: 9999px;
  padding: 0.125rem 0.5rem;
  font-size: 0.75rem;
  line-height: 1rem;
}

.badge-profit {
  background: #eff6ff; /* blue-50 */
  color: #1d4ed8;      /* blue-700 */
}

.icon-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 28px; height: 28px;
  border-radius: 0.5rem;
  transition: background 120ms ease;
}
.icon-btn:hover { background: rgba(15,23,42,0.06); }

/* Hide number input spinners for a cleaner look */
input[type=number]::-webkit-outer-spin-button,
input[type=number]::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
input[type=number] {
  -moz-appearance: textfield;
}

